using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Enums;
using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicApp.Pages;

public partial class LoginBase : ComponentBase
{
    [Inject] protected BffAuthenticationStateProvider AuthStateProvider { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;

    protected LoginModel loginModel = new();
    protected bool isLoggingIn = false;
    protected string errorMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await JSRuntime.InvokeVoidAsync("console.log", "Login page: OnInitializedAsync called");

        // Check if user is already authenticated via BFF
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        var isAuthenticated = authState.User.Identity?.IsAuthenticated == true;

        await JSRuntime.InvokeVoidAsync("console.log", $"Login page: User authenticated = {isAuthenticated}");

        if (isAuthenticated)
        {
            await JSRuntime.InvokeVoidAsync("console.log", "Login page: User already authenticated, redirecting to home");
            // Redirect to home page if already logged in
            Navigation.NavigateTo("/");
        }
    }

    protected async Task HandleLogin()
    {
        isLoggingIn = true;
        errorMessage = string.Empty;

        try
        {
            // Use BFF authentication state provider
            var success = await AuthStateProvider.LoginAsync(loginModel.LoginName, loginModel.Password);

            if (success)
            {
                await JSRuntime.InvokeVoidAsync("console.log", "BFF Login successful");

                // Get the current user to determine role
                var user = await AuthStateProvider.GetCurrentUserAsync();

                Navigation.NavigateTo("/");

                // Navigate based on user role
                //if (user?.RoleId == (int)UserRoleEnum.Administrator)
                //{
                //    Navigation.NavigateTo("/");
                //}
                //else if (user?.RoleId == (int)UserRoleEnum.Tutor || user?.RoleId == (int)UserRoleEnum.Student)
                //{
                //    Navigation.NavigateTo("/lessons");
                //}
                //else
                //{
                //    Navigation.NavigateTo("/");
                //}
            }
            else
            {
                errorMessage = "Invalid login name or password. Please try again.";
                await JSRuntime.InvokeVoidAsync("console.error", $"BFF Login failed: {errorMessage}");
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
            await JSRuntime.InvokeVoidAsync("console.error", $"Login error: {ex.Message}");
        }
        finally
        {
            isLoggingIn = false;
        }
    }

    protected async Task HandleKeyPress(Microsoft.AspNetCore.Components.Web.KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !isLoggingIn)
        {
            await HandleLogin();
        }
    }

    public class LoginModel
    {
        [Required(ErrorMessage = "Login name is required")]
        public string LoginName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;
    }
}
